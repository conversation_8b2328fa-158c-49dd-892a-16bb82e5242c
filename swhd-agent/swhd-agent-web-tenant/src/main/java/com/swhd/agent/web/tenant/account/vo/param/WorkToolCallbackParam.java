package com.swhd.agent.web.tenant.account.vo.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025/5/28 16:22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class WorkToolCallbackParam {

    // {"groupName":"三维互动广告充值测…","fileName":"","atMe":"false","filePath":"","groupRemark":"","spoken":"是的，这时候 大概率不生效了","textType":1,"rawSpoken":"是的，这时候 @ 大概率不生效了","receivedName":"钟俊滨","roomType":1}

    /**
     * 群名称
     */
    private String groupName;

    private String fileName;
    /**
     * 是否 @ 我（也就是当前机器人）
     */
    private Boolean atMe;


    private String filePath;
    /**
     * 群备注，后续 titleList 用到（推荐用这个）
     */
    private String groupRemark;
    /**
     * 消息类型 0=未知 1=文本 2=图片 3=语音 5=视频 7=小程序 8=链接 9=文件 13=合并记录 15=带回复文本
     */
    private Integer textType;
    /**
     * 原始消息文本
     */
    private String rawSpoken;
    /**
     * 消息文本（去掉 @ 和引用）
     */
    private String spoken;
    /**
     * 谁发的（后面放到 atList 里可以 @ 这个人）
     */
    private String receivedName;
    /**
     * 房间类型 1=外部群 2=外部联系人 3=内部群 4=内部联系人
     */
    private Integer roomType;

}
